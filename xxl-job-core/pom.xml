<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xuxueli</groupId>
		<artifactId>xxl-job</artifactId>
		<version>3.2.1-SNAPSHOT</version>
	</parent>
	<artifactId>xxl-job-core</artifactId>
	<packaging>jar</packaging>

	<name>${project.artifactId}</name>
	<description>A distributed task scheduling framework.</description>
	<url>https://www.xuxueli.com/</url>

	<dependencies>

		<!-- ********************** embed server: netty + gson ********************** -->
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-codec-http</artifactId>
			<version>${netty.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>

		<!-- ********************** plugin ********************** -->
		<!-- groovy-all -->
		<dependency>
			<groupId>org.apache.groovy</groupId>
			<artifactId>groovy</artifactId>
			<version>${groovy.version}</version>
		</dependency>

		<!-- spring-context -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- ********************** base ********************** -->
		<!-- slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j-api.version}</version>
		</dependency>

		<!-- jakarta.annotation-api -->
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
			<version>${jakarta.annotation-api.version}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

</project>